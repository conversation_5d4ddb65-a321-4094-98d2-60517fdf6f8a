{"name": "pg-cloudflare", "version": "1.2.7", "description": "A socket implementation that can run on Cloudflare Workers using native TCP connections.", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "devDependencies": {"ts-node": "^8.5.4", "typescript": "^4.0.3"}, "exports": {".": {"workerd": {"import": "./esm/index.mjs", "require": "./dist/index.js"}, "default": "./dist/empty.js"}}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "prepublish": "yarn build", "test": "echo e2e test in pg package"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg-cloudflare"}, "files": ["/dist/*{js,ts,map}", "/src", "/esm"], "gitHead": "8f8e7315e8f7c1bb01e98fdb41c8c92585510782"}