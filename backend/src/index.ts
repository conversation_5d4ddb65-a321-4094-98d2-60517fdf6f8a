/**
 * DATABASE MIGRATION NOTICE
 *
 * This application has been fully migrated from Sequelize to Prisma ORM.
 * All database operations now use Prisma for improved type safety and performance.
 *
 * The migration was completed on May 13, 2024.
 * All Sequelize-related files have been completely removed on May 15, 2024.
 */

import express, { ErrorRe<PERSON>Handler, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import morgan from 'morgan';
import path from 'path';
import cookieParser from 'cookie-parser';
import compression from 'compression';
import helmet from 'helmet';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { initPrismaWithMiddleware } from './middleware/prismaMiddleware';
import validateEnv, { getEnv, getBoolEnv, getNumEnv } from './utils/envValidator';
import { validateCsrfToken, generateCsrfToken } from './middleware/csrf.middleware';
import apiCache from './middleware/apiCache.middleware';
import validation from './middleware/validation.middleware';
import rateLimiting from './middleware/rateLimiting.middleware';

// Import routes
import authRoutes from './routes/auth';
import authCookieRoutes from './routes/auth.cookie.routes';
import scholarshipRoutes from "./routes/scholarship.routes";
import userRoutes from './routes/user.routes';
import adminCookieRoutes from "./routes/admin.cookie.routes";
import adminPasswordRoutes from './routes/admin.password.routes';
import twoFactorRoutes from './routes/twoFactor.routes';
import messagesRoutes from './routes/messages';
import newsletterRoutes from './routes/newsletter';

// Validate and load environment variables
if (!validateEnv()) {
  console.error('Environment validation failed. Exiting application.');
  process.exit(1);
}

// Log application startup information
console.info(`Starting MaBourse backend in ${process.env.NODE_ENV} mode`);
console.info(`Server port: ${process.env.PORT}`);
console.info(`Database: ${process.env.DATABASE_URL?.replace(/:[^:]*@/, ':****@')}`);

// Initialize Prisma client (simplified for debugging)
const prisma = new PrismaClient();

// Create Express app
const app = express();

// Middleware
// Configure CORS based on environment
const corsOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:3001'];
app.use(cors({
  origin: corsOrigins, // Use specific origins instead of true for better security
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
}));

// Log CORS configuration
console.info(`CORS configured for origins: ${corsOrigins.join(', ')}`);

// Basic middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser()); // Add cookie parser middleware
app.use(morgan('dev'));

// Security middleware
app.use(helmet()); // Add security headers
app.use(compression()); // Compress responses
app.use(validation.securityHeaders); // Add additional security headers
app.use(validation.sanitizeUrlParams); // Sanitize URL parameters

// Caching middleware for public endpoints
app.use(apiCache.addCacheHeaders()); // Add cache headers to responses

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Apply general API rate limiting to all routes
app.use('/api', rateLimiting.apiLimiter);

// Routes
// Legacy routes (token in Authorization header)
app.use('/api/auth', rateLimiting.authLimiter, authRoutes);

// New secure routes (token in HTTP-only cookie)
// Apply CSRF protection to secure routes
app.use('/api/v2', generateCsrfToken); // Generate CSRF token for all secure routes
app.use('/api/v2', validateCsrfToken); // Validate CSRF token for all secure routes
app.use('/api/v2/auth', rateLimiting.authLimiter, authCookieRoutes);
app.use('/api/v2/admin', adminCookieRoutes);

// Register admin cookie routes at the root level as well for backward compatibility
app.use('/api/admin', adminCookieRoutes);

// Shared routes with caching for public endpoints
app.use('/api/scholarships', apiCache.cacheApiResponse(300), validation.sanitizeRequestBody, scholarshipRoutes);
app.use('/api/users', validation.sanitizeRequestBody, userRoutes);
app.use('/api/messages', rateLimiting.contactFormLimiter, validation.sanitizeRequestBody, messagesRoutes);
app.use('/api/newsletter', rateLimiting.contactFormLimiter, validation.sanitizeRequestBody, newsletterRoutes);
// Admin password routes must be registered before admin routes to avoid authentication middleware
app.use('/api/admin/password', rateLimiting.passwordResetLimiter, validation.sanitizeRequestBody, adminPasswordRoutes);
// Two-factor authentication routes
app.use('/api/2fa', rateLimiting.authLimiter, validation.sanitizeRequestBody, twoFactorRoutes);

// Health check endpoint with caching
app.get('/api/health', apiCache.cacheApiResponse(60), (req, res) => {
  res.json({
    status: 'ok',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Test database connection endpoint
app.get('/api/test-db', async (req, res) => {
  try {
    // Test admin query
    const adminCount = await prisma.admin.count();

    // Test user query
    const userCount = await prisma.user.count();

    // Test scholarship query
    const scholarshipCount = await prisma.scholarship.count();

    res.json({
      success: true,
      message: 'Database connection is working properly',
      data: {
        adminCount,
        userCount,
        scholarshipCount
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection test failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

// Cache statistics endpoint (admin only)
app.get('/api/admin/cache-stats', (req, res) => {
  if (!req.user || !req.user.isMainAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Access denied',
      error: 'Admin privileges required'
    });
  }

  res.json({
    success: true,
    message: 'Cache statistics retrieved successfully',
    data: {
      apiCache: apiCache.getCacheStats(),
      queryCache: require('./middleware/prismaQueryOptimizer').getCacheStats()
    }
  });
});

// Error handling middleware
const errorHandler: ErrorRequestHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);

  // Log the error
  const { apiLogger } = require('./utils/logger');
  apiLogger.error(`Unhandled error: ${err.message}`, {
    stack: err.stack,
    path: req.originalUrl,
    method: req.method,
    ip: req.ip,
    user: req.user ? { id: req.user.id, role: req.user.role } : undefined
  });

  // Send error response
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined,
    requestId: req.headers['x-request-id'] || undefined,
    timestamp: new Date().toISOString()
  });
};

app.use(errorHandler);

// Import cleanup utilities
import { performDataCleanup } from './utils/cleanupUtils';

// Initialize database and start server
const PORT = process.env.PORT || 5000;

// Start server
const startServer = async () => {
  try {
    // Connect to Prisma database
    await prisma.$connect();
    console.log('Prisma database connection established successfully.');

    // Check if main admin exists in Prisma
    const mainAdmin = await prisma.admin.findFirst({
      where: { isMainAdmin: true }
    });

    // Only create admin if none exists
    if (!mainAdmin) {
      console.log('Creating main admin in Prisma...');
      // Hash the password
      const hashedPassword = await bcrypt.hash('admin123', 10);

      // Create main admin
      await prisma.admin.create({
        data: {
          name: 'Main Admin',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'super_admin',
          privileges: JSON.stringify(['all']),
          isMainAdmin: true,
        }
      });
      console.log('Main admin created successfully in Prisma.');
    }

    // Run data cleanup to fix any duplicate data issues
    // Temporarily disabled to fix server startup issues
    // console.log('Running data cleanup process...');
    // await performDataCleanup();

    // Start server
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
};

// Handle application shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

startServer();