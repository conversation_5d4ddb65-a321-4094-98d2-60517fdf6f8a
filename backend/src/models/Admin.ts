/**
 * Admin Model
 * 
 * This model handles all database operations for admin users
 */

import bcrypt from 'bcryptjs';
import { query, transaction } from '../config/database';
import { PoolClient } from 'pg';

export interface AdminData {
  id?: number;
  name: string;
  email: string;
  password?: string;
  role: string;
  privileges: string[];
  isMainAdmin: boolean;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  failedLoginAttempts: number;
  lockUntil?: Date;
  lastLogin?: Date;
  twoFactorSecret?: string;
  twoFactorEnabled: boolean;
  twoFactorTempSecret?: string;
  twoFactorBackupCodes?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

export class Admin {
  /**
   * Create a new admin
   */
  static async create(adminData: Omit<AdminData, 'id' | 'createdAt' | 'updatedAt'>): Promise<AdminData> {
    const hashedPassword = adminData.password ? await bcrypt.hash(adminData.password, 10) : null;
    
    const result = await query(`
      INSERT INTO admins (
        name, email, password, role, privileges, is_main_admin,
        failed_login_attempts, two_factor_enabled
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      adminData.name,
      adminData.email,
      hashedPassword,
      adminData.role,
      JSON.stringify(adminData.privileges),
      adminData.isMainAdmin,
      adminData.failedLoginAttempts || 0,
      adminData.twoFactorEnabled || false
    ]);
    
    return this.mapRowToAdmin(result.rows[0]);
  }

  /**
   * Find admin by ID
   */
  static async findById(id: number): Promise<AdminData | null> {
    const result = await query('SELECT * FROM admins WHERE id = $1', [id]);
    return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Find admin by email
   */
  static async findByEmail(email: string): Promise<AdminData | null> {
    const result = await query('SELECT * FROM admins WHERE email = $1', [email]);
    return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Find main admin
   */
  static async findMainAdmin(): Promise<AdminData | null> {
    const result = await query('SELECT * FROM admins WHERE is_main_admin = TRUE LIMIT 1');
    return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Get all admins
   */
  static async findAll(): Promise<AdminData[]> {
    const result = await query('SELECT * FROM admins ORDER BY created_at DESC');
    return result.rows.map(row => this.mapRowToAdmin(row));
  }

  /**
   * Update admin
   */
  static async update(id: number, updates: Partial<AdminData>): Promise<AdminData | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
        const dbKey = this.camelToSnake(key);
        
        if (key === 'password' && value) {
          // Hash password if provided
          fields.push(`${dbKey} = $${paramCount}`);
          values.push(bcrypt.hashSync(value as string, 10));
        } else if (key === 'privileges' || key === 'twoFactorBackupCodes') {
          // JSON stringify arrays
          fields.push(`${dbKey} = $${paramCount}`);
          values.push(JSON.stringify(value));
        } else {
          fields.push(`${dbKey} = $${paramCount}`);
          values.push(value);
        }
        paramCount++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    const result = await query(`
      UPDATE admins 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);

    return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
  }

  /**
   * Delete admin
   */
  static async delete(id: number): Promise<boolean> {
    const result = await query('DELETE FROM admins WHERE id = $1', [id]);
    return (result.rowCount || 0) > 0;
  }

  /**
   * Verify password
   */
  static async verifyPassword(admin: AdminData, password: string): Promise<boolean> {
    if (!admin.password) return false;
    return bcrypt.compare(password, admin.password);
  }

  /**
   * Update failed login attempts
   */
  static async updateFailedLoginAttempts(id: number, attempts: number, lockUntil?: Date): Promise<void> {
    await query(`
      UPDATE admins 
      SET failed_login_attempts = $1, lock_until = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [attempts, lockUntil, id]);
  }

  /**
   * Update last login
   */
  static async updateLastLogin(id: number): Promise<void> {
    await query(`
      UPDATE admins 
      SET last_login = CURRENT_TIMESTAMP, failed_login_attempts = 0, lock_until = NULL
      WHERE id = $1
    `, [id]);
  }

  /**
   * Count total admins
   */
  static async count(): Promise<number> {
    const result = await query('SELECT COUNT(*) as count FROM admins');
    return parseInt(result.rows[0].count);
  }

  /**
   * Map database row to AdminData
   */
  private static mapRowToAdmin(row: any): AdminData {
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      password: row.password,
      role: row.role,
      privileges: row.privileges ? JSON.parse(row.privileges) : [],
      isMainAdmin: row.is_main_admin,
      resetPasswordToken: row.reset_password_token,
      resetPasswordExpires: row.reset_password_expires,
      failedLoginAttempts: row.failed_login_attempts,
      lockUntil: row.lock_until,
      lastLogin: row.last_login,
      twoFactorSecret: row.two_factor_secret,
      twoFactorEnabled: row.two_factor_enabled,
      twoFactorTempSecret: row.two_factor_temp_secret,
      twoFactorBackupCodes: row.two_factor_backup_codes ? JSON.parse(row.two_factor_backup_codes) : [],
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Convert camelCase to snake_case
   */
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
