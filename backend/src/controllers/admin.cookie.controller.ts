import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import jwt, { SignOptions } from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { Admin } from '../models/Admin';
import { rateLimit } from 'express-rate-limit';
import {
  sendSuccess,
  sendError,
  sendNotFound,
  sendValidationError,
  sendUnauthorized,
  sendForbidden
} from '../utils/apiResponse';
import dateUtils from '../utils/dateUtils';

// Using PostgreSQL models directly instead of Prisma

// Rate limiting for login attempts
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts
  message: {
    success: false,
    message: 'Too many login attempts',
    error: 'Please try again after 15 minutes'
  },
});

/**
 * Admin login
 * @param req Request
 * @param res Response
 */
export const login = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { email, password } = req.body;

    // Find admin
    const admin = await prisma.admin.findUnique({
      where: { email }
    });

    if (!admin) {
      return sendUnauthorized(res, 'Invalid credentials');
    }

    // Check if account is locked
    if (admin.lockUntil && admin.lockUntil > new Date()) {
      return sendError(
        res,
        'Account is locked',
        'Please try again later or reset your password',
        423 // Locked
      );
    }

    // Check password
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      // Increment failed login attempts
      await prisma.admin.update({
        where: { id: admin.id },
        data: {
          failedLoginAttempts: admin.failedLoginAttempts + 1,
          lockUntil: admin.failedLoginAttempts >= 4 ? new Date(Date.now() + 30 * 60 * 1000) : null // Lock for 30 minutes after 5 failed attempts
        }
      });
      return sendUnauthorized(res, 'Invalid credentials');
    }

    // Reset failed login attempts and update last login
    await prisma.admin.update({
      where: { id: admin.id },
      data: {
        failedLoginAttempts: 0,
        lockUntil: null,
        lastLogin: new Date()
      }
    });

    // Parse privileges if stored as string
    const privileges = typeof admin.privileges === 'string'
      ? JSON.parse(admin.privileges)
      : admin.privileges;

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const jwtOptions: SignOptions = { expiresIn: '1d' }; // Fixed expiration time
    const token = jwt.sign(
      {
        id: admin.id,
        email: admin.email,
        role: admin.role,
        isMainAdmin: admin.isMainAdmin
      },
      jwtSecret,
      jwtOptions
    );

    // Set token in HTTP-only cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      path: '/'
    });

    return sendSuccess(
      res,
      {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled
        }
      },
      'Login successful'
    );
  } catch (error) {
    console.error('Admin login error:', error);
    return sendError(res, 'Login failed', error);
  }
};

/**
 * Admin logout
 * @param req Request
 * @param res Response
 */
export const logout = async (req: Request, res: Response) => {
  try {
    // Clear the token cookie
    res.clearCookie('token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
      path: '/'
    });

    return sendSuccess(res, null, 'Logout successful');
  } catch (error) {
    console.error('Admin logout error:', error);
    return sendError(res, 'Logout failed', error);
  }
};

/**
 * Get current admin profile
 * @param req Request
 * @param res Response
 */
export const getCurrentAdmin = async (req: Request, res: Response) => {
  try {
    const adminId = req.user?.id;

    if (!adminId) {
      return sendUnauthorized(res, 'Admin not authenticated');
    }

    const admin = await prisma.admin.findUnique({
      where: { id: adminId }
    });

    if (!admin) {
      return sendNotFound(res, 'Admin not found');
    }

    // Parse privileges if stored as string
    const privileges = typeof admin.privileges === 'string'
      ? JSON.parse(admin.privileges)
      : admin.privileges;

    return sendSuccess(
      res,
      {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled,
          lastLogin: admin.lastLogin,
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt
        }
      },
      'Admin profile retrieved successfully'
    );
  } catch (error) {
    console.error('Get admin profile error:', error);
    return sendError(res, 'Failed to retrieve admin profile', error);
  }
};

/**
 * Get all admins (main admin only)
 * @param req Request
 * @param res Response
 */
export const getAdmins = async (req: Request, res: Response) => {
  try {
    // This endpoint should only be accessible by main admin
    if (!req.user?.isMainAdmin) {
      return sendForbidden(res, 'Only main admin can access this resource');
    }

    const admins = await prisma.admin.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        privileges: true,
        isMainAdmin: true,
        twoFactorEnabled: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // Process privileges for each admin
    const processedAdmins = admins.map((admin: any) => ({
      ...admin,
      privileges: typeof admin.privileges === 'string'
        ? JSON.parse(admin.privileges)
        : admin.privileges
    }));

    return sendSuccess(res, { admins: processedAdmins }, 'Admins retrieved successfully');
  } catch (error) {
    console.error('Get admins error:', error);
    return sendError(res, 'Failed to retrieve admins', error);
  }
};
