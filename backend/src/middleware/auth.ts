import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { Admin } from '../models/Admin';
import { PrismaClient } from '@prisma/client';
import { UserPayload } from '../types/express';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Original middleware (renamed for backward compatibility)
export const authenticateToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, JWT_SECRET) as UserPayload;

    // Verify admin still exists
    const admin = await Admin.findById(decoded.id);
    if (!admin) {
      return res.status(401).json({ message: 'Admin not found' });
    }

    req.user = decoded;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ message: 'Invalid token' });
    }
    console.error('Auth middleware error:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// New middleware for tests
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ message: 'No token provided' });
    }

    // Check token format
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return res.status(401).json({ message: 'Invalid token format' });
    }

    const token = parts[1];

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as UserPayload;

    // Attach user info to request
    req.user = decoded;

    next();
  } catch (error) {
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// Original middleware (renamed for backward compatibility)
export const isMainAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user?.isMainAdmin) {
    return res.status(403).json({ message: 'Access denied. Main admin privileges required.' });
  }
  next();
};

// New middleware for tests
export const adminMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Access denied' });
  }

  next();
};

// New middleware for tests
export const mainAdminMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  if (req.user.role !== 'admin' || !req.user.isMainAdmin) {
    return res.status(403).json({ message: 'Main admin privileges required' });
  }

  next();
};

// New middleware for tests
export const ownershipMiddleware = (resourceType: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const resourceId = parseInt(req.params.id);
      if (isNaN(resourceId)) {
        return res.status(400).json({ message: 'Invalid resource ID' });
      }

      // Admin users can access any resource
      if (req.user.role === 'admin') {
        return next();
      }

      // For non-admin users, check ownership
      let resource: any = null;

      // Get the resource based on its type
      switch (resourceType) {
        case 'scholarship':
          resource = await prisma.scholarship.findUnique({
            where: { id: resourceId }
          });
          break;
        // Add other resource types as needed
        default:
          return res.status(500).json({ message: `Unknown resource type: ${resourceType}` });
      }

      if (!resource) {
        return res.status(404).json({ message: 'Resource not found' });
      }

      // Check if the user is the owner of the resource
      if (resource.createdBy !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized' });
      }

      next();
    } catch (error) {
      console.error('Ownership middleware error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  };
};