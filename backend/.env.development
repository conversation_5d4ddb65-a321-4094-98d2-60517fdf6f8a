PORT=5000
JWT_SECRET=mabourse-secure-jwt-secret-key-dev
JWT_EXPIRATION=1d
REFRESH_TOKEN_SECRET=mabourse-secure-refresh-token-key-dev
REFRESH_TOKEN_EXPIRATION=7d
NODE_ENV=development

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=MaBourse <<EMAIL>>

# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=mabourse

# Database URL for PostgreSQL
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mabourse

# Admin configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_NAME=Main Administrator

# CORS configuration
CORS_ORIGIN=http://localhost:3000

# Frontend URL for password reset links
FRONTEND_URL=http://localhost:3000

# Logging
LOG_LEVEL=debug

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
