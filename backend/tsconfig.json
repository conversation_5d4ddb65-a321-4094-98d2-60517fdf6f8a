{"compilerOptions": {"target": "es2018", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/__tests__/**/*", "src/scripts/**/*", "src/middleware/prisma*.ts", "src/index.prisma.ts", "src/controllers/account.recovery.controller.ts", "src/controllers/security.controller.ts", "src/controllers/scholarship.controller.ts", "src/controllers/user.controller.ts", "src/utils/cleanupUtils.ts"]}